import { system, world, BlockPermutation } from "@minecraft/server";
const type = "ditsh:type";
const tag = "ditsh:stairs";
const componentName = "ditsh:stairs";
const blocker = "ditsh:stairs_collision";
function getNeighbors(block) {
    return [block, block.north(), block.south(), block.east(), block.west(), block.above(), block.below()];
}
function updateNeighbors(block) {
    if (block.typeId.includes("minecraft") && block.typeId.includes("stairs")) {
        for (const neighbor of getNeighbors(block).slice(1)) {
            if (neighbor.hasTag(tag)) {
                system.run(() => update(neighbor));
            }
        }
    }
}
world.afterEvents.playerPlaceBlock.subscribe((eventData) => {
    updateNeighbors(eventData.block);
});
world.beforeEvents.playerBreakBlock.subscribe((eventData) => {
    updateNeighbors(eventData.block);
});
world.afterEvents.playerBreakBlock.subscribe((eventData) => {
    const block = eventData.block;
    for (const neighbor of [block.above(), block.below()]) {
        if (neighbor.hasTag(tag)) {
            system.run(() => update(neighbor));
        }
    }
});
world.afterEvents.pistonActivate.subscribe((eventData) => {
    const { piston, isExpanding } = eventData;
    const attachedBlocks = piston.getAttachedBlocks();
    const updatedPositions = new Set();
    for (const block of attachedBlocks) {
        updateBlocker(block);
        for (const neighbor of getNeighbors(block).slice(1)) {
            if (!neighbor || (!block.typeId.includes("minecraft") && !block.typeId.includes("stairs")) || !neighbor.hasTag(tag))
                continue;
            const key = `${neighbor.location.x},${neighbor.location.y},${neighbor.location.z}`;
            if (updatedPositions.has(key))
                continue;
            updatedPositions.add(key);
            system.run(() => {
                update(neighbor);
            });
        }
    }
});
world.afterEvents.explosion.subscribe((eventData) => {
    const impactedBlocks = eventData.getImpactedBlocks();
    const updatedPositions = new Set();
    for (const block of impactedBlocks) {
        updateBlocker(block);
        for (const neighbor of getNeighbors(block).slice(1)) {
            if (!neighbor || (!block.typeId.includes("minecraft") && !block.typeId.includes("stairs")) || !neighbor.hasTag(tag))
                continue;
            const key = `${neighbor.location.x},${neighbor.location.y},${neighbor.location.z}`;
            if (updatedPositions.has(key))
                continue;
            updatedPositions.add(key);
            system.run(() => {
                update(neighbor);
            });
        }
    }
});
export function registerStairsComponent(blockComponentRegistry) {
    blockComponentRegistry.registerCustomComponent(componentName, {
        onPlace(event) {
            const { block } = event;
            if (block.hasTag(tag)) {
                for (const neighbor of getNeighbors(block).slice()) {
                    if (neighbor.hasTag(tag)) {
                        system.run(() => update(neighbor));
                    }
                    else if (neighbor.typeId.includes("minecraft:") && neighbor.typeId.includes("stairs")) {
                        system.run(() => update(block));
                    }
                }
            }
            else {
                const half = block.permutation.getState("minecraft:verical_half");
                const target = half === "bottom" ? block.above() : block.below();
                if (!target.hasTag(tag)) {
                    block.setPermutation(BlockPermutation.resolve("minecraft:air"));
                }
            }
        },
        onPlayerBreak(event) {
            const { block } = event;
            if (block.typeId === blocker)
                return;
            updateBlocker(block);
            for (const neighbor of getNeighbors(block)) {
                if (neighbor.hasTag(tag)) {
                    system.run(() => update(neighbor));
                }
            }
        }
    });
}
function updateBlocker(block) {
    const above = block.above();
    const below = block.below();
    if (above?.typeId === blocker && above.permutation.getState("minecraft:vertical_half") === "bottom") {
        above.setPermutation(BlockPermutation.resolve("minecraft:air"));
    }
    else if (below?.typeId === blocker && below.permutation.getState("minecraft:vertical_half") === "top") {
        below.setPermutation(BlockPermutation.resolve("minecraft:air"));
    }
}
;
function update(block) {
    updateBlocker(block);
    if (!block || block.typeId === null)
        return;
    const north = block.north();
    const south = block.south();
    const east = block.east();
    const west = block.west();
    const above = block.above();
    const below = block.below();
    const direction = block.permutation.getState("minecraft:cardinal_direction");
    const stairHalf = block.permutation.getState("minecraft:vertical_half");
    function checkHalf(block) {
        return block?.permutation.getState("minecraft:vertical_half");
    }
    function checkDirection(block) {
        return block?.permutation.getState("minecraft:cardinal_direction");
    }
    function vanillaValid(neighbor, direction) {
        const upside = neighbor?.permutation.getState("upside_down_bit");
        const weirdo = neighbor?.permutation.getState("weirdo_direction");
        let parity;
        switch (weirdo) {
            case 0:
                parity = "east";
                break;
            case 1:
                parity = "west";
                break;
            case 2:
                parity = "south";
                break;
            case 3:
                parity = "north";
                break;
            default: return false;
        }
        return parity === direction && ((upside === false && stairHalf === "bottom") || (upside === true && stairHalf === "top"));
    }
    function validNeighbor(neighbor, direction) {
        return vanillaValid(neighbor, direction) || (checkHalf(neighbor) === stairHalf && checkDirection(neighbor) === direction);
    }
    let toPlace = 1;
    if (checkDirection(block) === "north") {
        if (validNeighbor(north, "west"))
            toPlace = 4;
        else if (validNeighbor(north, "east"))
            toPlace = 5;
        else if (validNeighbor(south, "west"))
            toPlace = 2;
        else if (validNeighbor(south, "east"))
            toPlace = 3;
        else if (!(checkHalf(north) === stairHalf || checkHalf(south) === stairHalf) ||
            (validNeighbor(west, "north") && validNeighbor(north, "east")) ||
            (validNeighbor(east, "north") && validNeighbor(north, "west")))
            toPlace = 1;
    }
    if (checkDirection(block) === "south") {
        if (validNeighbor(north, "west"))
            toPlace = 3;
        else if (validNeighbor(north, "east"))
            toPlace = 2;
        else if (validNeighbor(south, "west"))
            toPlace = 4;
        else if (validNeighbor(south, "east"))
            toPlace = 5;
        else if (!(checkHalf(north) === stairHalf || checkHalf(south) === stairHalf) ||
            (validNeighbor(east, "south") && validNeighbor(south, "west")) ||
            (validNeighbor(west, "south") && validNeighbor(south, "east")))
            toPlace = 1;
    }
    if (checkDirection(block) === "west") {
        if (validNeighbor(west, "north"))
            toPlace = 5;
        else if (validNeighbor(west, "south"))
            toPlace = 4;
        else if (validNeighbor(east, "north"))
            toPlace = 3;
        else if (validNeighbor(east, "south"))
            toPlace = 2;
        else if (!(checkHalf(west) === stairHalf || checkHalf(east) === stairHalf) ||
            (validNeighbor(south, "west") && validNeighbor(west, "north")) ||
            (validNeighbor(north, "west") && validNeighbor(west, "south")))
            toPlace = 1;
    }
    if (checkDirection(block) === "east") {
        if (validNeighbor(west, "north"))
            toPlace = 2;
        else if (validNeighbor(west, "south"))
            toPlace = 3;
        else if (validNeighbor(east, "north"))
            toPlace = 5;
        else if (validNeighbor(east, "south"))
            toPlace = 4;
        else if (!(checkHalf(west) === stairHalf || checkHalf(east) === stairHalf) ||
            (validNeighbor(north, "east") && validNeighbor(east, "south")) ||
            (validNeighbor(south, "east") && validNeighbor(east, "north")))
            toPlace = 1;
    }
    if (!block.hasTag(tag))
        return;
    block.setPermutation(block.permutation.withState(type, toPlace));
    const target = stairHalf === "bottom" ? above : below;
    if (target?.typeId === blocker || target?.isAir || target?.typeId === "minecraft:water" || target?.typeId.includes("piston_arm")) {
        let directionState = direction;
        if (toPlace === 4) {
            switch (direction) {
                case "north":
                    directionState = "west";
                    break;
                case "west":
                    directionState = "south";
                    break;
                default: return;
            }
        }
        else if (toPlace === 5) {
            switch (direction) {
                case "south":
                    directionState = "east";
                    break;
                case "east":
                    directionState = "north";
                    break;
                default: return;
            }
        }
        let water = false;
        if (target.typeId === "minecraft:water" && target.permutation.getState("liquid_depth") === 0)
            water = true;
        target.setPermutation(BlockPermutation.resolve(blocker)
            .withState("minecraft:cardinal_direction", directionState)
            .withState("minecraft:vertical_half", stairHalf)
            .withState("ditsh:corner", toPlace > 3 ? true : false));
        target.setWaterlogged(water);
    }
}
